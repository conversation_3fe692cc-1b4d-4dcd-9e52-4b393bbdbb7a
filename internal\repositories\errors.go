package repositories

import "fmt"

// Repository层自定义错误类型
// 这些错误类型在Repository层定义和使用，为上层提供有意义的错误信息

// UserNotFoundError 用户未找到错误
type UserNotFoundError struct {
	Field string // 查询字段名（如 "email", "username", "id"）
	Value string // 查询值
}

func (e *UserNotFoundError) Error() string {
	return fmt.Sprintf("user not found by %s=%s", e.Field, e.Value)
}

// UserExistsError 用户已存在错误
type UserExistsError struct {
	Field string // 冲突字段名
	Value string // 冲突值
}

func (e *UserExistsError) Error() string {
	return fmt.Sprintf("user already exists with %s=%s", e.Field, e.Value)
}

// DatabaseError 数据库操作错误
type DatabaseError struct {
	Operation string // 操作类型（如 "create", "update", "delete", "query"）
	Table     string // 表名
	Err       error  // 原始错误
}

func (e *DatabaseError) Error() string {
	return fmt.Sprintf("database %s operation failed on table %s: %v", e.Operation, e.Table, e.Err)
}

func (e *DatabaseError) Unwrap() error {
	return e.Err
}
