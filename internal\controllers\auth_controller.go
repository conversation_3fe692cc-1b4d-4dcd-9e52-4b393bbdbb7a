package controllers

import (
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/pkg/apperror"
	"strings"

	"github.com/gin-gonic/gin"
	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/consts"
	"shikeyinxiang/internal/middleware"
	"shikeyinxiang/internal/service"
)

// AuthController 依赖于 IAuth 服务接口
type AuthController struct {
	authSvc service.IAuth
}

// NewAuthController 创建一个新的 AuthController 实例
func NewAuthController(authSvc service.IAuth) *AuthController {
	return &AuthController{
		authSvc: authSvc,
	}
}

// handleError 统一处理错误响应
func (ac *AuthController) handleError(c *gin.Context, err error) {
	if customErr, ok := err.(*apperror.CustomError); ok {
		response.Error(c, customErr.Code)
	} else {
		response.Error(c, consts.CodeInternalError)
	}
}

// bindJSONAndValidate 统一处理JSON绑定和验证
func (ac *AuthController) bindJSONAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindJSON(req); err != nil {
		response.Error(c, consts.CodeParameterError)
		return false
	}
	return true
}

// getCurrentUserID 获取当前用户ID
func (ac *AuthController) getCurrentUserID(c *gin.Context) (int64, bool) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		response.Error(c, consts.CodeUnauthorized)
		return 0, false
	}
	return userID, true
}

// extractToken 从请求头提取token
func (ac *AuthController) extractToken(c *gin.Context) (string, bool) {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		response.Error(c, consts.CodeAuthMissingToken)
		return "", false
	}

	parts := strings.SplitN(authHeader, " ", 2)
	if !(len(parts) == 2 && parts[0] == "Bearer") {
		response.Error(c, consts.CodeAuthFormatError)
		return "", false
	}

	token := parts[1]
	if token == "" {
		response.Error(c, consts.CodeAuthMissingToken)
		return "", false
	}

	return token, true
}

// AdminLogin 管理员登录
func (ac *AuthController) AdminLogin(c *gin.Context) {
	var req v1.AdminLoginReq
	if !ac.bindJSONAndValidate(c, &req) {
		return
	}

	res, err := ac.authSvc.AdminLogin(c.Request.Context(), &req)
	if err != nil {
		ac.handleError(c, err)
		return
	}

	response.Success(c, res)
}

// UserLogin 用户登录
func (ac *AuthController) UserLogin(c *gin.Context) {
	var req v1.UserLoginReq
	if !ac.bindJSONAndValidate(c, &req) {
		return
	}

	res, err := ac.authSvc.UserLogin(c.Request.Context(), &req)
	if err != nil {
		ac.handleError(c, err)
		return
	}

	response.Success(c, res)
}

// WechatLogin 微信登录
func (ac *AuthController) WechatLogin(c *gin.Context) {
	var req v1.WechatLoginReq
	if !ac.bindJSONAndValidate(c, &req) {
		return
	}

	res, err := ac.authSvc.WechatLogin(c.Request.Context(), &req)
	if err != nil {
		ac.handleError(c, err)
		return
	}

	response.Success(c, res)
}

// Register 用户注册
func (ac *AuthController) Register(c *gin.Context) {
	var req v1.RegisterReq
	if !ac.bindJSONAndValidate(c, &req) {
		return
	}

	res, err := ac.authSvc.Register(c.Request.Context(), &req)
	if err != nil {
		ac.handleError(c, err)
		return
	}

	response.Success(c, res)
}

// Logout 用户登出
func (ac *AuthController) Logout(c *gin.Context) {
	token, ok := ac.extractToken(c)
	if !ok {
		return
	}

	err := ac.authSvc.Logout(c.Request.Context(), token)
	if err != nil {
		ac.handleError(c, err)
		return
	}

	response.Success(c, gin.H{"message": "登出成功"})
}

// ChangePassword 修改密码
func (ac *AuthController) ChangePassword(c *gin.Context) {
	var req v1.ChangePasswordReq
	if !ac.bindJSONAndValidate(c, &req) {
		return
	}

	userID, ok := ac.getCurrentUserID(c)
	if !ok {
		return
	}

	success, err := ac.authSvc.ChangePassword(c.Request.Context(), &req, userID)
	if err != nil {
		ac.handleError(c, err)
		return
	}

	response.Success(c, gin.H{"success": success})
}

// GetCurrentUser 获取当前用户信息
func (ac *AuthController) GetCurrentUser(c *gin.Context) {
	userID, ok := ac.getCurrentUserID(c)
	if !ok {
		return
	}

	userInfo, err := ac.authSvc.GetCurrentUser(c.Request.Context(), userID)
	if err != nil {
		ac.handleError(c, err)
		return
	}

	response.Success(c, userInfo)
}
