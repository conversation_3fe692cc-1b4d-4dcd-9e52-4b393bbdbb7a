package service

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
	"shikeyinxiang/internal/config"
)

// WechatService 微信服务
type WechatService struct {
	config *config.WechatConfig
	client *http.Client
}

// NewWechatService 创建微信服务实例
func NewWechatService(cfg *config.WechatConfig) *WechatService {
	return &WechatService{
		config: cfg,
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// WechatLoginResponse 微信登录响应
type WechatLoginResponse struct {
	OpenID     string `json:"openid"`
	SessionKey string `json:"session_key"`
	UnionID    string `json:"unionid,omitempty"`
	ErrCode    int    `json:"errcode,omitempty"`
	ErrMsg     string `json:"errmsg,omitempty"`
}

// GetOpenIDByCode 通过code获取用户openid
func (w *WechatService) GetOpenIDByCode(code string) (*WechatLoginResponse, error) {
	// 构建请求URL
	requestURL := fmt.Sprintf("%s?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
		w.config.LoginURL,
		w.config.AppID,
		w.config.Secret,
		url.QueryEscape(code))

	// 发送HTTP请求
	resp, err := w.client.Get(requestURL)
	if err != nil {
		return nil, fmt.Errorf("failed to request wechat api: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read wechat response: %w", err)
	}

	// 解析JSON响应
	var wechatResp WechatLoginResponse
	if err := json.Unmarshal(body, &wechatResp); err != nil {
		return nil, fmt.Errorf("failed to parse wechat response: %w", err)
	}

	// 检查微信返回的错误
	if wechatResp.ErrCode != 0 {
		return nil, fmt.Errorf("wechat api error: code=%d, msg=%s", wechatResp.ErrCode, wechatResp.ErrMsg)
	}

	// 检查必要字段
	if wechatResp.OpenID == "" {
		return nil, fmt.Errorf("wechat response missing openid")
	}

	return &wechatResp, nil
}
