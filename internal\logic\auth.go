package logic

import (
	"context"
	"errors"
	"fmt"
	"log"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/consts"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/jwt"
	"shikeyinxiang/internal/pkg"
	"shikeyinxiang/internal/repositories"
	"shikeyinxiang/internal/service"
)

// authLogic 认证业务逻辑实现
type authLogic struct {
	userRepo repositories.IUserRepo
}

// NewAuth 创建认证业务逻辑实例
func NewAuth(userRepo repositories.IUserRepo) service.IAuth {
	return &authLogic{
		userRepo: userRepo,
	}
}

// AdminLogin 管理员登录
func (a *authLogic) AdminLogin(ctx context.Context, req *v1.AdminLoginReq) (*v1.LoginRes, error) {
	// 参数验证
	if req.Username == "" {
		return nil, fmt.Errorf("username is required")
	}

	// 根据用户名查找用户
	user, err := a.userRepo.GetByUsername(req.Username)
	if err != nil {
		// 检查是否是用户未找到错误
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return nil, &InvalidCredentialsError{}
		}
		return nil, fmt.Errorf("service: failed to get user for admin login: %w", err)
	}

	// 验证密码
	if !pkg.VerifyPassword(user.Password, req.Password) {
		return nil, &InvalidCredentialsError{}
	}

	// 验证用户角色
	if user.Role != consts.RoleAdmin {
		return nil, &InsufficientPermissionsError{
			RequiredRole: consts.RoleAdmin,
			UserRole:     user.Role,
		}
	}

	// 检查用户状态
	if user.Status != consts.UserStatusActive {
		return nil, &AccountDisabledError{UserID: user.ID}
	}

	// 生成JWT令牌
	token, err := jwt.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return nil, &TokenGenerationError{Err: err}
	}

	// 构造响应
	userInfo := &v1.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		AvatarURL:  user.AvatarURL,
		CreateTime: user.CreateTime,
	}

	return &v1.LoginRes{
		Token:    token,
		UserInfo: userInfo,
	}, nil
}

// UserLogin 普通用户登录
func (a *authLogic) UserLogin(ctx context.Context, req *v1.UserLoginReq) (*v1.LoginRes, error) {
	// 参数验证
	if req.Email == "" {
		return nil, fmt.Errorf("email is required")
	}

	// 根据邮箱查找用户
	user, err := a.userRepo.GetByEmail(req.Email)
	if err != nil {
		// 检查是否是用户未找到错误
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return nil, &InvalidCredentialsError{}
		}
		return nil, fmt.Errorf("service: failed to get user for login: %w", err)
	}

	// 验证密码
	if !pkg.VerifyPassword(user.Password, req.Password) {
		return nil, &InvalidCredentialsError{}
	}

	// 验证用户角色
	if user.Role != consts.RoleUser {
		return nil, &InsufficientPermissionsError{
			RequiredRole: consts.RoleUser,
			UserRole:     user.Role,
		}
	}

	// 检查用户状态
	if user.Status != consts.UserStatusActive {
		return nil, &AccountDisabledError{UserID: user.ID}
	}

	// 生成JWT令牌
	token, err := jwt.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return nil, &TokenGenerationError{Err: err}
	}

	// 构造响应
	userInfo := &v1.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		AvatarURL:  user.AvatarURL,
		CreateTime: user.CreateTime,
	}

	return &v1.LoginRes{
		Token:    token,
		UserInfo: userInfo,
	}, nil
}

// WechatLogin 微信登录
func (a *authLogic) WechatLogin(ctx context.Context, req *v1.WechatLoginReq) (*v1.LoginRes, error) {
	// TODO: 实现微信登录逻辑
	// 1. 使用code向微信服务器获取openid
	// 2. 根据openid查找或创建用户
	// 3. 生成JWT令牌
	return nil, apperror.New(consts.CodeInternalError, fmt.Errorf("微信登录功能暂未实现"))
}

// Register 用户注册
func (a *authLogic) Register(ctx context.Context, req *v1.RegisterReq) (*v1.RegisterRes, error) {
	// 检查用户名是否已存在
	exists, err := a.userRepo.ExistsByUsername(req.Username)
	if err != nil {
		log.Printf("检查用户名是否存在失败: %v", err)
		return nil, apperror.New(consts.CodeInternalError, err)
	}
	if exists {
		return nil, apperror.New(consts.CodeUsernameExists, nil)
	}

	// 检查邮箱是否已存在
	exists, err = a.userRepo.ExistsByEmail(req.Email)
	if err != nil {
		log.Printf("检查邮箱是否存在失败: %v", err)
		return nil, apperror.New(consts.CodeInternalError, err)
	}
	if exists {
		return nil, apperror.New(consts.CodeEmailExists, nil)
	}

	// 加密密码
	hashedPassword, err := pkg.HashPassword(req.Password)
	if err != nil {
		log.Printf("密码加密失败: %v", err)
		return nil, apperror.New(consts.CodeInternalError, err)
	}

	// 创建用户实体
	user := entities.NewUser(req.Username, req.Email, hashedPassword, consts.RoleUser)

	// 保存用户
	if err := a.userRepo.Create(user); err != nil {
		log.Printf("创建用户失败: %v", err)
		return nil, apperror.New(consts.CodeInternalError, err)
	}

	// 构造响应
	userInfo := &v1.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		AvatarURL:  user.AvatarURL,
		CreateTime: user.CreateTime,
	}

	return &v1.RegisterRes{
		User: userInfo,
	}, nil
}

// Logout 用户登出
func (a *authLogic) Logout(ctx context.Context, token string) error {
	// 将token加入黑名单
	if err := jwt.BlacklistToken(token); err != nil {
		log.Printf("将token加入黑名单失败: %v", err)
		return apperror.New(consts.CodeInternalError, err)
	}
	return nil
}

// ChangePassword 修改密码
func (a *authLogic) ChangePassword(ctx context.Context, req *v1.ChangePasswordReq, userID int64) (bool, error) {
	// 获取当前用户信息
	user, err := a.userRepo.GetByID(userID)
	if err != nil {
		log.Printf("查询用户失败: %v", err)
		return false, apperror.New(consts.CodeInternalError, err)
	}

	if user == nil {
		return false, apperror.New(consts.CodeNotFound, nil)
	}

	// 验证旧密码
	if !pkg.VerifyPassword(user.Password, req.OldPassword) {
		return false, apperror.New(consts.CodeUserPasswordError, nil)
	}

	// 加密新密码
	hashedPassword, err := pkg.HashPassword(req.NewPassword)
	if err != nil {
		log.Printf("密码加密失败: %v", err)
		return false, apperror.New(consts.CodeInternalError, err)
	}

	// 更新密码
	if err := a.userRepo.UpdatePassword(userID, hashedPassword); err != nil {
		log.Printf("更新密码失败: %v", err)
		return false, apperror.New(consts.CodeInternalError, err)
	}

	return true, nil
}

// GetCurrentUser 获取当前用户信息
func (a *authLogic) GetCurrentUser(ctx context.Context, userId int64) (*v1.UserInfo, error) {
	user, err := a.userRepo.GetByID(userId)
	if err != nil {
		log.Printf("查询用户失败: %v", err)
		return nil, apperror.New(consts.CodeInternalError, err)
	}

	if user == nil {
		return nil, apperror.New(consts.CodeNotFound, nil)
	}

	// 构造响应
	userInfo := &v1.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		AvatarURL:  user.AvatarURL,
		CreateTime: user.CreateTime,
	}

	return userInfo, nil
}
