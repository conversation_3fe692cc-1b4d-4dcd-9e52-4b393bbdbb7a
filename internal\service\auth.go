package service

import (
	"context"
	v1 "shikeyinxiang/api/v1"
)

// IAuth 认证服务接口，定义了所有与认证相关的业务能力
type IAuth interface {
	// AdminLogin 管理员登录
	AdminLogin(ctx context.Context, req *v1.AdminLoginReq) (*v1.LoginRes, error)

	// UserLogin 普通用户登录
	UserLogin(ctx context.Context, req *v1.UserLoginReq) (*v1.LoginRes, error)

	// WechatLogin 微信登录
	WechatLogin(ctx context.Context, req *v1.WechatLoginReq) (*v1.LoginRes, error)
	
	// Register 用户注册
	Register(ctx context.Context, req *v1.RegisterReq) (*v1.RegisterRes, error)

	// Logout 用户登出
	Logout(ctx context.Context, token string) error

	// ChangePassword 修改密码
	ChangePassword(ctx context.Context, req *v1.ChangePasswordReq, userID int64) (bool, error)

	// GetCurrentUser 获取当前用户信息
	GetCurrentUser(ctx context.Context, userId int64) (*v1.UserInfo, error)
}

// 全局服务实例
var (
	Auth IAuth
)
