package routes

import (
	"github.com/gin-gonic/gin"
	"shikeyinxiang/internal/controllers"
	"shikeyinxiang/internal/database"
	"shikeyinxiang/internal/logic"
	"shikeyinxiang/internal/middleware"
	"shikeyinxiang/internal/repositories"
)

// SetupRoutes 设置所有路由
func SetupRoutes(r *gin.Engine) {
	// 应用全局中间件
	r.Use(middleware.CORSMiddleware())
	r.Use(middleware.ErrorHandler())
	// TODO: 添加其他中间件
	// r.Use(middleware.LoggerMiddleware())
	// r.Use(middleware.RequestIDMiddleware())

	// TODO: 设置404和405处理器
	// r.NoRoute(middleware.NotFoundHandler())
	// r.NoMethod(middleware.MethodNotAllowedHandler())

	// 健康检查接口
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Service is running",
		})
	})

	// API路由组
	api := r.Group("/api")
	{
		// 认证相关路由 /api/auth
		setupAuthRoutes(api)

		// TODO: 其他路由将在后续实现
		// setupUserRoutes(api)
		// setupFoodRoutes(api)
		// setupDietRecordRoutes(api)
		// setupNutritionRoutes(api)
		// setupFileRoutes(api)
		// setupAdminRoutes(api)
	}
}

// setupAuthRoutes 设置认证路由
func setupAuthRoutes(api *gin.RouterGroup) {
	// 初始化依赖
	userRepo := repositories.NewUserRepository(database.GetDB())
	wechatSvc := service.NewWechatService(&config.AppConfig.Wechat)
	authLogic := logic.NewAuth(userRepo, wechatSvc)
	authController := controllers.NewAuthController(authLogic)

	auth := api.Group("/auth")
	{
		// 公开路由（无需认证）
		auth.POST("/register", authController.Register)
		auth.POST("/user/login", authController.UserLogin)
		auth.POST("/admin/login", authController.AdminLogin)
		auth.POST("/wechat-login", authController.WechatLogin)

		// 需要认证的路由
		auth.Use(middleware.AuthMiddleware()) // 对下面的路由组统一应用认证中间件
		{
			auth.POST("/logout", authController.Logout)
			auth.POST("/change-password", authController.ChangePassword)
			auth.GET("/me", authController.GetCurrentUser)
		}
	}
}
