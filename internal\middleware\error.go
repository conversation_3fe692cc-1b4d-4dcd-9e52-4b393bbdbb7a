package middleware

import (
	"errors"
	"github.com/gin-gonic/gin"
	"log"
	"net/http"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/consts"
	"shikeyinxiang/internal/pkg/apperror"
)

// ErrorHandler 是一个全局错误处理中间件
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 在所有请求处理完毕后，检查是否有错误
		if len(c.Errors) == 0 {
			// 检查路由是否存在
			if c.Writer.Status() == http.StatusNotFound {
				response.Error(c, consts.CodeNotFound)
			}
			return
		}

		// 只处理最新的一个错误
		err := c.Errors.Last().Err

		var customErr *apperror.CustomError
		if errors.As(err, &customErr) {
			// 如果是自定义的业务错误，则按预定义的业务码返回
			response.Error(c, customErr.Code)
			return
		}

		// 对于参数绑定等非自定义的、但应该返回400的错误
		// 这里的检查可以更具体，例如使用 validator.ValidationErrors 类型断言
		// 为了简化，我们暂时认为所有非CustomError的controller级错误都是参数错误
		// 更稳健的做法是让controller在绑定错误时也抛出带特定code的CustomError
		if c.Writer.Status() == http.StatusBadRequest || c.Writer.Status() == 0 {
			log.Printf("Parameter or other client error: %v", err)
			response.Error(c, consts.CodeParameterError)
			return
		}

		// 如果都不是，则认为是未知的服务器内部错误
		log.Printf("Internal server error: %v", err)
		response.Error(c, consts.CodeInternalError)
	}
}
