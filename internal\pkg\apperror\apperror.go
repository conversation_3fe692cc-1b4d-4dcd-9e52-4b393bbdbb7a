package apperror

import "shikeyinxiang/internal/consts"

// CustomError 是一个自定义错误类型，用于在服务层向上传递业务错误
type CustomError struct {
	Code    int   // 业务错误码
	OrigErr error // 原始错误
}

// Error 实现了 error 接口
func (e *CustomError) Error() string {
	if e.OrigErr != nil {
		return e.OrigErr.Error()
	}
	// 使用 consts 包来获取标准消息
	return consts.GetMessage(e.Code)
}

// New 创建一个新的 CustomError
func New(code int, err error) *CustomError {
	return &CustomError{Code: code, OrigErr: err}
}
